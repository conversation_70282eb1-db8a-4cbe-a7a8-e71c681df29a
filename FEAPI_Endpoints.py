import os
import FEAPI_Request as r


class FEAPI_Endpoints:

    def __init__(self, api_address, security_address, username, passw, tenant, token=''):
        self.feapi_request = r.FEAPIRequest(api_address, security_address, username, passw, tenant)


    def ValidateUserEntry(self, userentry):
        
        api_response = self.feapi_request.make_post_request("search/validateuserentry","'"+userentry+"'")
        return api_response
    




if __name__ == '__main__':
    
    # ------------------------ body ------------------------
    # Determine API and Security URLs
    my_api = 'https://feapidevatl.callminer.net/api/v2/'
    my_sec = 'https://sapidevatl.callminer.net/'
    username = os.getenv("FEAPI_username")
    password = os.getenv("FEAPI_pwd")
    tenant = "devatl"

    # call API
    endpoints = FEAPI_Endpoints(my_api, my_sec, username, password, tenant)

    api_response = endpoints.ValidateUserEntry("hello")
    print (f"api_response = {api_response}")
    api_response = endpoints.ValidateUserEntry("\"hello world\"")
    print (f"api_response = {api_response}")
 
    
