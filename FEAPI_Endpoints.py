import os
import FEAPI_Request as r


class FEAPI_Endpoints:

    def __init__(self, api_address, security_address, username, passw, tenant, token=''):
        self.feapi_request = r.FEAPIRequest(api_address, security_address, username, passw, tenant)

    def FormatUserEntry(self, userentry: str) -> str:
        """
        Formats a user entry string by escaping existing double quotes and
        enclosing the entire string in double quotes.

        Args:
            userentry: The input string from the user.

        Returns:
            The formatted string with escaped double quotes and
            enclosed in double quotes.
        """
        # Step 1: Escape any double quotes already present in the userentry string
        # Replace each " with \"
        escaped_entry = userentry.replace('"', '\\"')

        # Step 2: Add double quotes around the entire escaped string
        # This creates the final desired format: "original string with escaped quotes"
        formatted_string = f'"{escaped_entry}"'

        return formatted_string

    def ValidateUserEntry(self, userentry):
        userentryformatted = self.FormatUserEntry(userentry)

        api_response = self.feapi_request.make_post_request("search/validateuserentry",userentryformatted)
        return api_response
    




if __name__ == '__main__':
    
    # ------------------------ body ------------------------
    # Determine API and Security URLs
    my_api = 'https://feapidevatl.callminer.net/api/v2/'
    my_sec = 'https://sapidevatl.callminer.net/'
    username = os.getenv("FEAPI_username")
    password = os.getenv("FEAPI_pwd")
    tenant = "devatl"

    # call API
    endpoints = FEAPI_Endpoints(my_api, my_sec, username, password, tenant)

    api_response = endpoints.ValidateUserEntry("hello")
    print (f"api_response = {api_response}")
    api_response = endpoints.ValidateUserEntry("\"hello world\"")
    print (f"api_response = {api_response}")
 
    
