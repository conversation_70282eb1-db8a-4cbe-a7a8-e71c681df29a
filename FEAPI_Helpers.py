import os
import csv
import pandas as pd
from typing import List, Dict, Any, Union, Optional
from FEAPI_Endpoints import FEAPI_Endpoints

class FEAPI_Helpers:
    """
    Helper class for CallMiner FEAPI operations.
    Stores API connection information and provides utility functions.
    """
    
    def __init__(self, 
                api_address: str = None, 
                security_address: str = None, 
                username: str = None, 
                password: str = None, 
                tenant: str = None):
        """
        Initialize the FEAPI_Helpers with connection information.
        
        Args:
            api_address: The FEAPI endpoint URL
            security_address: The security API URL
            username: CallMiner username
            password: CallMiner password
            tenant: CallMiner tenant name
        """
        # Use provided values or defaults
        self.api_address = api_address or 'https://feapi.callminer.net/api/v2/'
        self.security_address = security_address or 'https://sapicm.callminer.net/'
        self.username = username or os.getenv("FEAPI_username")
        self.password = password or os.getenv("FEAPI_pwd")
        self.tenant = tenant or "training02"
        
        # Initialize the FEAPI client
        self.endpoints = FEAPI_Endpoints(
            self.api_address, 
            self.security_address, 
            self.username, 
            self.password, 
            self.tenant
        )
    
    def process_user_entries(self, user_entries: List[str]) -> Dict[str, Any]:
        """
        Process a list of user entries and return results as a dictionary.
        
        Args:
            user_entries: List of user entry strings to validate
            
        Returns:
            Dictionary with user entries as keys and validation results as values
        """
        results = {}
        
        for entry in user_entries:
            try:
                # Call the ValidateUserEntry method for each entry
                response = self.endpoints.ValidateUserEntry(entry)
                results[entry] = response
            except Exception as e:
                # Store the error message if validation fails
                results[entry] = f"Error: {str(e)}"
        
        return results

    def process_csv_file(self, 
                        input_file_path: str, 
                        user_entry_column: str = "userentry",
                        output_column: str = "validation_result") -> str:
        """
        Process user entries from a CSV file and save results to a new CSV file.
        
        Args:
            input_file_path: Path to the input CSV file
            user_entry_column: Name of the column containing user entries to validate
            output_column: Name of the column to store validation results
        
        Returns:
            Path to the output CSV file
        
        Raises:
            FileNotFoundError: If the input file doesn't exist
            ValueError: If the specified column doesn't exist in the CSV
        """
        # Check if file exists
        if not os.path.exists(input_file_path):
            raise FileNotFoundError(f"Input file not found: {input_file_path}")
        
        # Generate output file path
        output_file_path = f"{os.path.splitext(input_file_path)[0]}.output.csv"
        
        # Read the CSV file
        try:
            df = pd.read_csv(input_file_path, quoting=csv.QUOTE_NONE)
        except Exception as e:
            raise ValueError(f"Error reading CSV file: {str(e)}")
        
        # Check if the column exists
        if user_entry_column not in df.columns:
            raise ValueError(f"Column '{user_entry_column}' not found in the CSV file")
        

        # Create a new column for results using the mapping
        validation_results = []

        for entry in df[user_entry_column].astype(str):
            response = ""
            # Wrap the entry in single quotes for the API
            try:
                # Call the ValidateUserEntry method for each entry
                response = self.endpoints.ValidateUserEntry(entry)
            except Exception as e:
                # Store the error message if validation fails
                response = f"Error: {str(e)}"
        
            validation_results.append(response)

        
        df[output_column] = validation_results
        
        # Save to output file
        df.to_csv(output_file_path, index=True)
        
        return output_file_path


if __name__ == '__main__':
    # Example usage
    helpers = FEAPI_Helpers()
    
    # Example 1: Process a list of user entries
    print("Example 1: Processing a list of user entries")
    test_entries = ["hello", "\"hello world\"", "\"test|hello query\""]
    results = helpers.process_user_entries(test_entries)
    
    for entry, result in results.items():
        print(f"Entry: {entry}")
        print(f"Result: {result}")
        print("-" * 50)
    
    # Example 2: Process a CSV file from testdata folder
    print("\nExample 2: Processing a CSV file from testdata folder")
    try:
        # Use the existing CSV file in the testdata folder
        test_csv_path = "testdata/test.userentry.csv"
        
        print(f"Using CSV file: {test_csv_path}")
        
        # Process the CSV file
        output_path = helpers.process_csv_file(test_csv_path, "userentry")
        print(f"Processed CSV saved to: {output_path}")
        
        # Display the results
        result_df = pd.read_csv(output_path)
        print("\nResults (first 5 rows):")
        print(result_df.head(5))
        
    except Exception as e:
        print(f"Error in CSV processing example: {str(e)}")







